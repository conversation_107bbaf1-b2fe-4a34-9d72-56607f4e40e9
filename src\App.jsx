import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Rocket, 
  Palette, 
  Code, 
  Zap, 
  Github, 
  Star,
  Heart,
  Check,
  ArrowRight,
  Sparkles,
  Shield,
  Users
} from 'lucide-react';
import { ThemeProvider } from '@/components/theme-provider';
import { ThemeToggle } from '@/components/theme-toggle';
import { toast } from 'sonner';
import { Toaster } from '@/components/ui/sonner';

function App() {
  const [selectedFeature, setSelectedFeature] = useState(null);

  const features = [
    {
      icon: <Rocket className="h-6 w-6" />,
      title: "Lightning Fast",
      description: "Built with Vite for blazing fast development and optimized production builds.",
      details: "Experience instant HMR, optimized bundling, and tree-shaking out of the box."
    },
    {
      icon: <Palette className="h-6 w-6" />,
      title: "Beautiful UI Components",
      description: "Pre-built shadcn/ui components with Tailwind CSS for rapid development.",
      details: "Access to 50+ professionally designed, accessible components ready to use."
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: "Modern JavaScript",
      description: "Latest ES6+ features with JSX support and modern tooling.",
      details: "Enjoy the latest JavaScript features without the complexity of TypeScript."
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Production Ready",
      description: "Optimized builds, proper error boundaries, and best practices included.",
      details: "Deploy with confidence using industry-standard patterns and optimizations."
    }
  ];

  const handleGetStarted = () => {
    toast.success("🎉 Welcome to your React.js starter!", {
      description: "Start building your next amazing project!",
    });
  };

  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <header className="flex items-center justify-between mb-12">
            <div className="flex items-center gap-2">
              <div className="h-10 w-10 bg-primary rounded-lg flex items-center justify-center">
                <Zap className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold">React Starter</h1>
                <p className="text-sm text-muted-foreground">with shadcn/ui</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="secondary" className="hidden sm:flex">
                <Star className="h-3 w-3 mr-1" />
                Production Ready
              </Badge>
              <ThemeToggle />
            </div>
          </header>

          {/* Hero Section */}
          <section className="text-center mb-16">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-purple-500/20 blur-3xl -z-10 rounded-full transform scale-150" />
              <div className="relative">
                <Badge variant="outline" className="mb-4">
                  <Sparkles className="h-3 w-3 mr-1" />
                  New Project Started
                </Badge>
                <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-6">
                  Build Amazing Apps
                  <br />
                  <span className="text-primary">Lightning Fast</span>
                </h2>
                <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                  A modern React.js starter with shadcn/ui components, Tailwind CSS, and everything you need to build beautiful applications.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Button size="lg" onClick={handleGetStarted} className="group">
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                  <Button variant="outline" size="lg" asChild>
                    <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                      <Github className="mr-2 h-4 w-4" />
                      View on GitHub
                    </a>
                  </Button>
                </div>
              </div>
            </div>
          </section>

          {/* Features Grid */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4">Why Choose This Starter?</h3>
              <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                Everything you need to build modern web applications with the latest technologies and best practices.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <Card 
                  key={index} 
                  className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border-2 hover:border-primary/50"
                  onClick={() => setSelectedFeature(selectedFeature === index ? null : index)}
                >
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-lg">{feature.title}</CardTitle>
                        <CardDescription>{feature.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  {selectedFeature === index && (
                    <CardContent className="pt-0">
                      <Separator className="mb-4" />
                      <p className="text-sm text-muted-foreground">{feature.details}</p>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          </section>

          {/* Interactive Demo Section */}
          <section className="mb-16">
            <Card className="max-w-4xl mx-auto">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Interactive Component Demo</CardTitle>
                <CardDescription>
                  Explore some of the beautiful components included in this starter
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="buttons" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="buttons">Buttons</TabsTrigger>
                    <TabsTrigger value="cards">Cards</TabsTrigger>
                    <TabsTrigger value="badges">Badges</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="buttons" className="space-y-4">
                    <div className="flex flex-wrap gap-4 justify-center">
                      <Button>Primary Button</Button>
                      <Button variant="secondary">Secondary</Button>
                      <Button variant="outline">Outline</Button>
                      <Button variant="ghost">Ghost</Button>
                      <Button variant="destructive">Destructive</Button>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="cards" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {[1, 2, 3].map((i) => (
                        <Card key={i}>
                          <CardHeader>
                            <CardTitle className="text-base">Sample Card {i}</CardTitle>
                            <CardDescription>
                              Beautiful card component with shadcn/ui
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-muted-foreground">
                              This is a sample card showcasing the design system.
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="badges" className="space-y-4">
                    <div className="flex flex-wrap gap-2 justify-center">
                      <Badge>Default</Badge>
                      <Badge variant="secondary">Secondary</Badge>
                      <Badge variant="destructive">Destructive</Badge>
                      <Badge variant="outline">Outline</Badge>
                      <Badge className="bg-green-500">
                        <Check className="h-3 w-3 mr-1" />
                        Success
                      </Badge>
                      <Badge className="bg-purple-500">
                        <Heart className="h-3 w-3 mr-1" />
                        Custom
                      </Badge>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </section>

          {/* Tech Stack */}
          <section className="mb-16">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Built With Modern Tech Stack</h3>
              <p className="text-muted-foreground">
                Carefully selected technologies for the best developer experience
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[
                { name: 'React 18', version: '^18.3.1' },
                { name: 'Vite', version: '^5.4.8' },
                { name: 'Tailwind CSS', version: '^3.4.13' },
                { name: 'shadcn/ui', version: 'Latest' },
              ].map((tech, index) => (
                <Card key={index} className="text-center p-4">
                  <CardContent className="p-0">
                    <h4 className="font-semibold">{tech.name}</h4>
                    <p className="text-sm text-muted-foreground">{tech.version}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Footer */}
          <footer className="text-center py-8 border-t">
            <div className="flex items-center justify-center gap-4 mb-4">
              <Badge variant="outline">
                <Users className="h-3 w-3 mr-1" />
                Developer Friendly
              </Badge>
              <Badge variant="outline">
                <Zap className="h-3 w-3 mr-1" />
                Fast & Efficient
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Built with ❤️ using React.js, shadcn/ui, and Tailwind CSS
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Start building your next amazing project today!
            </p>
          </footer>
        </div>
      </div>
      <Toaster />
    </ThemeProvider>
  );
}

export default App;